package za.co.wethinkcode.robots.commands;

import za.co.wethinkcode.robots.Robot;
import za.co.wethinkcode.robots.server.World;
import za.co.wethinkcode.robots.server.Response;
import za.co.wethinkcode.robots.handlers.VisibilityHandler;
import org.json.JSONObject;
import org.json.JSONArray;

public class LookCommand extends Command {
    public LookCommand(Robot robot, String[] arguments) {
        super(robot, arguments);
    }

    @Override
    public String commandName() {
        return "look";
    }

    @Override
    public Response execute(World world) {
        Robot foundRobot = world.findRobot(robot.getName());
        if (foundRobot == null) {
            return new Response("ERROR", "Could not find robot: " + robot.getName());
        }

        // Get visibility objects
        VisibilityHandler visibilityHandler = new VisibilityHandler(
            world.getRobots(),
            world.getObstacles(),
            world.getHalfWidth(),
            world.getHalfHeight(),
            world.getVisibility(),
            world
        );
        
        Response visibilityResponse = visibilityHandler.lookAround(foundRobot);

        // Create proper response structure
        JSONObject data = new JSONObject();
        data.put("objects", visibilityResponse.object.getJSONArray("objects"));

        // Add position as array [x, y] - the test expects this!
        JSONArray position = new JSONArray();
        position.put(foundRobot.getX());
        position.put(foundRobot.getY());
        data.put("position", position);

        JSONObject state = new JSONObject();
        state.put("direction", foundRobot.getDirection().toString());

        return Response.ok(data, "Done", state);
    }
}


package za.co.wethinkcode.robots.commands;
import za.co.wethinkcode.robots.Robot;
import za.co.wethinkcode.robots.server.Response;
import za.co.wethinkcode.robots.server.World;

public class StateCommand extends Command {
    public StateCommand(Robot robot, String[] arguments) {
        super(robot, arguments);
    }

    @Override
    public String commandName() {
        return "state";
    }

    @Override
    public Response execute(World world) {
        Robot foundRobot = world.findRobot(robot.getName());
        if (foundRobot != null) {
            String message = "\n" +
                    "State for " + robot.getName() + " 🤖:" +
                    "\n" +
                    " 🌎 Position: [" + foundRobot.getX() + "," + foundRobot.getY() + "]" +
                    "\n" +
                    " 🧭 Direction: " + foundRobot.getDirection().getDirection().symbolForDirection() +
                    "\n" +
                    " 🛡️ Shields: " + foundRobot.getShields() +
                    "\n" +
                    " 🔫 Shots: " + foundRobot.getShots() +
                    "\n" +
                    " 📋 Status: " + foundRobot.getStatus().toString().toUpperCase() +
                    "\n";

            Response response = new Response("OK", message);
            world.stateForRobot(foundRobot, response);
            return response;
        } else {
            return createErrorResponse("Could not find robot: " + robot.getName());
        }
    }

    private Response createErrorResponse(String message) {
        Response response = new Response("ERROR", "");
        org.json.JSONObject data = new org.json.JSONObject();
        data.put("message", message);
        response.object.put("data", data);
        return response;
    }
}

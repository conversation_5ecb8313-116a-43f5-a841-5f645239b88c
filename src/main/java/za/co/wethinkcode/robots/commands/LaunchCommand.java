package za.co.wethinkcode.robots.commands;

import org.json.JSONArray;
import org.json.JSONObject;
import za.co.wethinkcode.robots.Robot;
import za.co.wethinkcode.robots.server.Response;
import za.co.wethinkcode.robots.server.Status;
import za.co.wethinkcode.robots.server.World;

public class LaunchCommand extends Command {
    public LaunchCommand(Robot robot, String[] arguments) {
        super(robot, arguments);
    }

    @Override
    public String commandName() {
        return "launch";
    }

    @Override
    public Response execute(World world) {
        Status status = world.addRobot(robot);
        Response response;

        switch (status) {
            case OK -> {
                JSONObject data = new JSONObject();
                JSONArray position = new JSONArray().put(robot.getX()).put(robot.getY());
                data.put("position", position);
                data.put("visibility", world.getVisibility());
                data.put("reload", world.getReloadTime());
                data.put("repair", world.getShieldRepairTime());
                data.put("shields", world.getMaxShieldStrength());

                response = new Response("OK", "Ready");
                response.object.put("data", data);
                world.stateForRobot(robot, response);
            }
            case WORLDFULL -> response = new Response("ERROR", "No more space in this world");
            case ExistingName -> response = new Response("ERROR", "Too many of you in this world");
            case OutOfBounds -> response = new Response("ERROR", "Cannot place robot outside the world");
            case HitObstacle -> response = new Response("ERROR", "Robot cannot be placed on an obstacle");
            default -> response = new Response("ERROR", "Could not launch robot");
        }

        return response;
    }
}

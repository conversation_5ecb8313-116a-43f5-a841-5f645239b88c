## 🐳 Docker Usage Guide for EchoServer
This guide covers how to pull, run, and push a specific Docker image from the GitLab registry.

## 🔐 Authenticate with GitLab Container Registry
## 1. Login
⚠️ You must use a Personal Access Token with at least read_registry and write_registry permissions instead of your GitLab password.
# docker login -u <username> -p <your-personal-access-token> gitlab.wethinkco.de:5050


## 2. Build the image (optional, if you're editing code)
# docker build -t gitlab.wethinkco.de:5050/<username>/<container-name> .
# docker buildx build -t gitlab.wethinkco.de:5050<username>/<container-name> .

## 2.1 📥 Pull the Image
To pull the latest version of the echoserver from the GitLab registry:
# docker pull gitlab.wethinkco.de:5050/<username>/<container-name>:latest

## 2.2 viewing images
# docker images

## removing an image
# docker ps -a >> Listing all containers
# docker stop <containerID>
#  docker rm <containerID>
#  docker image rm gitlab.wethinkco.de:5050<username>/<container-name>


## 🚀 Run the Container
## 4. Run it locally
Run the image locally and expose the container port 9000 to your machine's port 9002:
# docker run -p 9002:9000 gitlab.wethinkco.de:5050/<username>/<container-name>:latest
Now you can access the server at http://localhost:9002.

## 📤 Push Your Image
## 3. Push to registry
After building or updating the image locally, tag it and push it to the registry:
# docker push gitlab.wethinkco.de:5050/<username>/<container-name>
Make sure you're logged in before pushing.

## 📝 Incase you come across "PERMISSION DENIED" error
Use the following commands on MacOS/Linux

groups:
    this is to check if docker is part of your pc groups

## this is to remove docker from your pc if there is faulty installation
sudo apt remove docker docker.io containerd runc:

## this is to install docker dependencies
sudo apt update:
sudo apt install -y ca-certificates curl gnupg lsb-release:
    this is to install docker dependencies

## this is to add docker keyring to your pc:
sudo mkdir -p /etc/apt/keyrings:
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg:
    
## To set up a stable repo
echo \
"deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
$(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

## To install Docker:
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

## To verify Docker installation:
sudo docker run hello-world

## Add Your User to Docker Group:
sudo usermod -aG docker $USER

## Force Group Permission Update:
newgrp docker

## To check memebership:
Groups

## Fix Socket Permissions:
sudo chown root:docker /var/run/docker.sock
sudo chmod 660 /var/run/docker.sock

## To check Docker Version:
docker --version

## Restart Docker:
sudo systemctl restart docker

##  Test Without Sudo:
docker run hello-world

## build your image:
docker build -t gitlab.wethinkco.de:5050/<username>/<container-name> .

## push your image:
docker push gitlab.wethinkco.de:5050/<username>/<container-name>    
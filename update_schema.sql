-- Check if columns exist before adding them (MySQL 5.7 compatible version)
SET @dbname = DATABASE();
SET @tablename = 'obstacles';
SET @columnname = 'world_id';
SET @preparedStatement = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      (TABLE_SCHEMA = @dbname)
      AND (TABLE_NAME = @tablename)
      AND (COLUMN_NAME = @columnname)
  ) > 0,
  'SELECT 1',
  CONCAT('ALTER TABLE ', @tablename, ' ADD COLUMN ', @columnname, ' INT;')
));
PREPARE alterIfNotExists FROM @preparedStatement;
EXECUTE alterIfNotExists;
DEALLOCATE PREPARE alterIfNotExists;

SET @tablename = 'robots';
SET @preparedStatement = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      (TABLE_SCHEMA = @dbname)
      AND (TABLE_NAME = @tablename)
      AND (COLUMN_NAME = @columnname)
  ) > 0,
  'SELECT 1',
  CONCAT('ALTER TABLE ', @tablename, ' ADD COLUMN ', @columnname, ' INT;')
));
PREPARE alterIfNotExists FROM @preparedStatement;
EXECUTE alterIfNotExists;
DEALLOCATE PREPARE alterIfNotExists;

-- Set default values for existing records
UPDATE obstacles SET world_id = 1 WHERE world_id IS NULL;
UPDATE robots SET world_id = 1 WHERE world_id IS NULL;

-- Add NOT NULL constraint
ALTER TABLE obstacles MODIFY COLUMN world_id INT NOT NULL;
ALTER TABLE robots MODIFY COLUMN world_id INT NOT NULL;

-- Add foreign key constraints
ALTER TABLE obstacles ADD CONSTRAINT fk_obstacles_world
    FOREIGN KEY (world_id) REFERENCES worlds(world_id) ON DELETE CASCADE;

ALTER TABLE robots ADD CONSTRAINT fk_robots_world
    FOREIGN KEY (world_id) REFERENCES worlds(world_id) ON DELETE CASCADE;